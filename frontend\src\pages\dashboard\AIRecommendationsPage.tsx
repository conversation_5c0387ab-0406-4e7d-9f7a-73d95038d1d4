/**
 * AI Recommendations Page
 * Shows user's AI recommendations in the dashboard
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { Bot, Target, TrendingUp, AlertTriangle, BarChart3, Filter, Search } from 'lucide-react';
// MainLayout removed - handled by routing system

import { useCentralizedAI } from '../../hooks/useCentralizedAI';
import { aiRecommendationsAPI } from '../../services/aiRecommendationsApi';

const AIRecommendationsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { status, isStatusLoading, statusError } = useCentralizedAI();

  // Fetch real AI recommendations data
  React.useEffect(() => {
    const fetchRecommendations = async () => {
      try {
        setLoading(true);
        setError(null);

        const data = await aiRecommendationsAPI.getRecommendations();
        setRecommendations(data || []);
      } catch (err) {
        console.error('Error fetching AI recommendations:', err);
        setError('Failed to load AI recommendations');
        setRecommendations([]);
      } finally {
        setLoading(false);
      }
    };

    fetchRecommendations();
  }, []);

  // Transform recommendations into recent actions format
  const recentActions = recommendations.map(rec => ({
    id: rec.id,
    type: rec.recommendation_type || 'general',
    title: rec.title,
    description: rec.description,
    business_idea: rec.business_idea?.title || 'Business Idea',
    timestamp: new Date(rec.created_at)
  }));

  const isLoading = loading || isStatusLoading;
  const displayError = error || statusError;

  // Simple time ago function
  const timeAgo = (date: Date) => {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInDays > 0) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    } else if (diffInHours > 0) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'automatic_enhancement':
        return <Bot className="h-5 w-5 text-indigo-500" />;
      case 'opportunity':
        return <Target className="h-5 w-5 text-green-500" />;
      case 'risk_assessment':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'progress_acceleration':
        return <TrendingUp className="h-5 w-5 text-blue-500" />;
      case 'market_analysis':
        return <BarChart3 className="h-5 w-5 text-orange-500" />;
      default:
        return <Bot className="h-5 w-5 text-gray-500" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'automatic_enhancement':
        return t('ai.types.enhancement', 'Enhancement');
      case 'opportunity':
        return t('ai.types.opportunity', 'Opportunity');
      case 'risk_assessment':
        return t('ai.types.risk', 'Risk Assessment');
      case 'progress_acceleration':
        return t('ai.types.progress', 'Progress');
      case 'market_analysis':
        return t('ai.types.market', 'Market Analysis');
      default:
        return type;
    }
  };

  const filteredActions = recentActions?.filter(action => {
    const matchesType = filterType === 'all' || action.type === filterType;
    const matchesSearch = searchTerm === '' ||
      action.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      action.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesType && matchesSearch;
  }) || [];

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-glass-primary">
              🤖 {t('ai.recommendations.title', 'AI Recommendations')}
            </h1>
            <p className="mt-1 text-glass-secondary">
              {t('ai.recommendations.subtitle', 'View all AI-generated recommendations for your business ideas')}
            </p>
          </div>
        </div>

        {/* Filters */}
        <ThemeWrapper
          className="card p-4"
          darkClassName="bg-indigo-900/30 border-indigo-800/30"
          lightClassName="bg-white border-slate-200"
        >
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <ThemeWrapper
                  as={Search}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4"
                  darkClassName="text-slate-400"
                  lightClassName="text-slate-500"
                />
                <ThemeWrapper
                  as="input"
                  type="text"
                  placeholder={t('ai.search.placeholder', 'Search recommendations...')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input w-full pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                  darkClassName="bg-indigo-800/30 border-indigo-700/30 text-white placeholder-slate-400"
                  lightClassName="bg-white border-slate-300 text-slate-800 placeholder-slate-500"
                />
              </div>
            </div>

            {/* Type Filter */}
            <div className="flex items-center space-x-2">
              <ThemeWrapper
                as={Filter}
                className="h-4 w-4"
                darkClassName="text-slate-400"
                lightClassName="text-slate-500"
              />
              <ThemeWrapper
                as="select"
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="input rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                darkClassName="bg-indigo-800/30 border-indigo-700/30 text-white"
                lightClassName="bg-white border-slate-300 text-slate-800"
              >
                <option value="all">{t('ai.filter.all', 'All Types')}</option>
                <option value="automatic_enhancement">{t('ai.types.enhancement', 'Enhancement')}</option>
                <option value="opportunity">{t('ai.types.opportunity', 'Opportunity')}</option>
                <option value="risk_assessment">{t('ai.types.risk', 'Risk Assessment')}</option>
                <option value="progress_acceleration">{t('ai.types.progress', 'Progress')}</option>
                <option value="market_analysis">{t('ai.types.market', 'Market Analysis')}</option>
              </ThemeWrapper>
            </div>
          </div>
        </ThemeWrapper>

        {/* Recommendations List */}
        <ThemeWrapper
          className="card"
          darkClassName="bg-indigo-900/30 border-indigo-800/30"
          lightClassName="bg-white border-slate-200"
        >
          <div className="p-6">
            <ThemeWrapper
              as="h2"
              className="text-lg font-semibold mb-4"
              darkClassName="text-white"
              lightClassName="text-slate-800"
            >
              {t('ai.recommendations.list', 'Your AI Recommendations')} ({filteredActions.length})
            </ThemeWrapper>

            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map(i => (
                  <ThemeWrapper
                    key={i}
                    className="skeleton-pulse rounded-lg h-20"
                    darkClassName="bg-indigo-800/30"
                    lightClassName="bg-slate-200"
                  />
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <ThemeWrapper
                  as="p"
                  className="text-red-500"
                  darkClassName="text-red-400"
                  lightClassName="text-red-600"
                >
                  {error}
                </ThemeWrapper>
              </div>
            ) : filteredActions.length > 0 ? (
              <div className="space-y-4">
                {filteredActions.map((action) => (
                  <ThemeWrapper
                    key={action.id}
                    className="p-4 rounded-lg border transition-all duration-300 hover:scale-[1.02]"
                    darkClassName="bg-indigo-800/20 border-indigo-700/30 hover:bg-indigo-800/30"
                    lightClassName="bg-slate-50 border-slate-200 hover:bg-slate-100"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        {getActionIcon(action.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <ThemeWrapper
                            as="h3"
                            className="text-sm font-semibold truncate"
                            darkClassName="text-white"
                            lightClassName="text-slate-800"
                          >
                            {action.title}
                          </ThemeWrapper>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs px-2 py-1 bg-indigo-600 text-white rounded-full">
                              {getTypeLabel(action.type)}
                            </span>
                            <ThemeWrapper
                              as="span"
                              className="text-xs"
                              darkClassName="text-slate-400"
                              lightClassName="text-slate-500"
                            >
                              {timeAgo(action.timestamp)}
                            </ThemeWrapper>
                          </div>
                        </div>
                        <ThemeWrapper
                          as="p"
                          className="text-sm mb-2"
                          darkClassName="text-slate-300"
                          lightClassName="text-slate-600"
                        >
                          {action.description}
                        </ThemeWrapper>
                        {action.business_idea && (
                          <ThemeWrapper
                            className="flex items-center space-x-2 text-xs"
                            darkClassName="text-slate-400"
                            lightClassName="text-slate-500"
                          >
                            <span>{t('ai.forIdea', 'For idea:')}</span>
                            <span className="font-medium">{action.business_idea}</span>
                          </ThemeWrapper>
                        )}
                      </div>
                    </div>
                  </ThemeWrapper>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <ThemeWrapper
                  as={Bot}
                  className="h-16 w-16 mx-auto mb-4"
                  darkClassName="text-slate-400"
                  lightClassName="text-slate-500"
                />
                <ThemeWrapper
                  as="h3"
                  className="text-lg font-medium mb-2"
                  darkClassName="text-white"
                  lightClassName="text-slate-800"
                >
                  {t('ai.noRecommendations.title', 'No AI Recommendations Yet')}
                </ThemeWrapper>
                <ThemeWrapper
                  as="p"
                  className="mb-4"
                  darkClassName="text-slate-400"
                  lightClassName="text-slate-600"
                >
                  {t('ai.noRecommendations.description', 'Create a business idea to get AI-powered recommendations and insights.')}
                </ThemeWrapper>
                <ThemeWrapper
                  as="a"
                  href="/dashboard/business-ideas"
                  className="btn-primary inline-flex items-center px-4 py-2 rounded-lg transition-all duration-300 hover:scale-105"
                  darkClassName="bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:shadow-glow"
                  lightClassName="bg-gradient-to-r from-indigo-500 to-purple-500 text-white hover:shadow-md"
                >
                  {t('ai.createIdea', 'Create Business Idea')}
                </ThemeWrapper>
              </div>
            )}
          </div>
        </ThemeWrapper>
      </div>
              </div>
        </div>
      </div>
    </div>
  );
};

export default AIRecommendationsPage;
