/**
 * Super Admin Dashboard Content Provider
 * Provides super admin-specific dashboard data, stats, and actions
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAppSelector } from '../../store/hooks';
import { DashboardStat, DashboardQuickAction } from '../../types/dashboard';
import { superAdminApi } from '../../services/superAdminApi';
import {
  Users,
  Shield,
  Activity,
  DollarSign,
  Settings,
  BarChart3,
  Crown,
  AlertTriangle,
  Database,
  Server,
  Lock,
  Monitor
} from 'lucide-react';

interface SuperAdminDashboardData {
  stats: DashboardStat[];
  quickActions: DashboardQuickAction[];
  systemMetrics: any;
  securityAlerts: any[];
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
}

const SuperAdminDashboardContext = createContext<SuperAdminDashboardData | null>(null);

interface SuperAdminDashboardProviderProps {
  children: React.ReactNode;
}

export const SuperAdminDashboardProvider: React.FC<SuperAdminDashboardProviderProps> = ({ children }) => {
  const { user } = useAppSelector(state => state.auth);
  const [stats, setStats] = useState<DashboardStat[]>([]);
  const [quickActions, setQuickActions] = useState<DashboardQuickAction[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<any>(null);
  const [securityAlerts, setSecurityAlerts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch super admin dashboard data
  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch real dashboard data from API
      const dashboardResponse = await superAdminApi.getDashboardData();
      const systemHealthResponse = await superAdminApi.getSystemHealth();
      const securityResponse = await superAdminApi.getSecurityEvents();

      if (!dashboardResponse.success) {
        throw new Error(dashboardResponse.error || 'Failed to fetch dashboard data');
      }

      const dashboardData = dashboardResponse.data;
      const systemHealth = systemHealthResponse.success ? systemHealthResponse.data : null;
      const securityData = securityResponse.success ? securityResponse.data : null;

      // Transform API data into dashboard stats
      const superAdminStats: DashboardStat[] = [
        {
          id: 'total_users',
          title: 'Total Users',
          value: dashboardData.user_stats?.total_users || 0,
          icon: Users,
          color: 'bg-blue-600/30',
          change: dashboardData.user_stats?.growth_rate || 0,
          changeType: (dashboardData.user_stats?.growth_rate || 0) >= 0 ? 'increase' : 'decrease',
          description: 'All registered users across the platform'
        },
        {
          id: 'system_health',
          title: 'System Health',
          value: systemHealth?.system_overview?.overall_status === 'healthy' ? '99.8%' : '85.0%',
          icon: Shield,
          color: 'bg-green-600/30',
          change: systemHealth?.system_overview?.health_trend || 0,
          changeType: (systemHealth?.system_overview?.health_trend || 0) >= 0 ? 'increase' : 'decrease',
          description: 'Overall system uptime and performance'
        },
        {
          id: 'active_sessions',
          title: 'Active Sessions',
          value: dashboardData.user_stats?.active_users || 0,
          icon: Activity,
          color: 'bg-purple-600/30',
          change: dashboardData.user_stats?.session_growth || 0,
          changeType: (dashboardData.user_stats?.session_growth || 0) >= 0 ? 'increase' : 'decrease',
          description: 'Current active user sessions'
        },
        {
          id: 'revenue',
          title: 'Monthly Revenue',
          value: dashboardData.financial_stats?.monthly_revenue || 0,
          icon: DollarSign,
          color: 'bg-emerald-600/30',
          change: dashboardData.financial_stats?.revenue_growth || 0,
          changeType: (dashboardData.financial_stats?.revenue_growth || 0) >= 0 ? 'increase' : 'decrease',
          suffix: 'USD',
          description: 'Platform revenue this month'
        }
      ];

      // Mock super admin quick actions - system-level controls
      const superAdminQuickActions: DashboardQuickAction[] = [
        {
          id: 'system_management',
          title: 'System Management',
          description: 'Manage core system settings and configuration',
          icon: Settings,
          href: '/super_admin/system-management',
          color: 'bg-red-600/20 hover:bg-red-600/30 border-red-500/30',
        },
        {
          id: 'user_management',
          title: 'Global User Management',
          description: 'Manage all users and administrative permissions',
          icon: Users,
          href: '/super_admin/users',
          color: 'bg-blue-600/20 hover:bg-blue-600/30 border-blue-500/30',
        },
        {
          id: 'system_monitoring',
          title: 'System Monitoring',
          description: 'Monitor system health, performance, and metrics',
          icon: Monitor,
          href: '/super_admin/monitoring',
          color: 'bg-green-600/20 hover:bg-green-600/30 border-green-500/30',
        },
        {
          id: 'security_center',
          title: 'Security Center',
          description: 'Manage security settings, logs, and alerts',
          icon: Lock,
          href: '/super_admin/security',
          color: 'bg-purple-600/20 hover:bg-purple-600/30 border-purple-500/30',
          badge: 3, // Mock security alerts count
        },
        {
          id: 'database_management',
          title: 'Database Management',
          description: 'Manage database operations and backups',
          icon: Database,
          href: '/super_admin/database',
          color: 'bg-orange-600/20 hover:bg-orange-600/30 border-orange-500/30',
        },
        {
          id: 'server_management',
          title: 'Server Management',
          description: 'Manage server infrastructure and deployment',
          icon: Server,
          href: '/super_admin/servers',
          color: 'bg-gray-600/20 hover:bg-gray-600/30 border-gray-500/30',
        }
      ];

      // Transform system metrics from API data
      const realSystemMetrics = {
        cpuUsage: systemHealth?.system_overview?.cpu?.usage_percent || 0,
        memoryUsage: systemHealth?.system_overview?.memory?.percent || 0,
        diskUsage: systemHealth?.system_overview?.disk?.percent || 0,
        networkTraffic: systemHealth?.network_status || 'Normal',
        databaseConnections: systemHealth?.database_connections || 0,
        activeProcesses: systemHealth?.active_processes || 0,
        uptime: systemHealth?.uptime || 'Unknown',
        lastBackup: systemHealth?.last_backup || 'Unknown'
      };

      // Transform security alerts from API data
      const realSecurityAlerts = securityData?.recent_events || [];

      setStats(superAdminStats);
      setQuickActions(superAdminQuickActions);
      setSystemMetrics(realSystemMetrics);
      setSecurityAlerts(realSecurityAlerts);

    } catch (err) {
      console.error('Error fetching super admin dashboard data:', err);
      setError('Failed to load super admin dashboard data');
      
      // Set empty data on error - no fallback mock data
      setStats([]);
      setQuickActions([]);
      setSystemMetrics({
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
        networkTraffic: 'Unknown',
        databaseConnections: 0,
        activeProcesses: 0,
        uptime: 'Unknown',
        lastBackup: 'Unknown'
      });
      setSecurityAlerts([]);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Initial data fetch
  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user, fetchDashboardData]);

  const contextValue: SuperAdminDashboardData = {
    stats,
    quickActions,
    systemMetrics,
    securityAlerts,
    loading,
    error,
    refreshData: fetchDashboardData,
  };

  return (
    <SuperAdminDashboardContext.Provider value={contextValue}>
      {children}
    </SuperAdminDashboardContext.Provider>
  );
};

// Hook to use super admin dashboard data
export const useSuperAdminDashboard = (): SuperAdminDashboardData => {
  const context = useContext(SuperAdminDashboardContext);
  if (!context) {
    throw new Error('useSuperAdminDashboard must be used within a SuperAdminDashboardProvider');
  }
  return context;
};

export default SuperAdminDashboardProvider;
