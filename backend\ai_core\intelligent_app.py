"""
Intelligent Application Core
Main AI orchestrator that transforms the platform from chat to intelligent app
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from django.contrib.auth.models import User
from django.db.models import Q, Count, Avg
from django.utils import timezone

from incubator.models import BusinessIdea, MentorProfile
from forums.models import ForumThread, ForumPost
# Use the new centralized AI service instead of deprecated ai_recommendations
from core.ai_service import ai_generate_intelligent_content, ai_is_available

logger = logging.getLogger(__name__)


class ContextEngine:
    """
    Maintains real-time understanding of user's business context
    """

    def __init__(self):
        self.context_cache = {}
        self.cache_ttl = 300  # 5 minutes

    async def get_user_context(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive user context"""
        cache_key = f"user_context_{user_id}"

        # Check cache first
        if cache_key in self.context_cache:
            cached_data = self.context_cache[cache_key]
            if datetime.now() - cached_data['timestamp'] < timedelta(seconds=self.cache_ttl):
                return cached_data['context']

        # Build fresh context
        context = await self._build_user_context(user_id)

        # Cache the result
        self.context_cache[cache_key] = {
            'context': context,
            'timestamp': datetime.now()
        }

        return context

    async def _build_user_context(self, user_id: int) -> Dict[str, Any]:
        """Build comprehensive user context from all platform data"""
        try:
            user = User.objects.get(id=user_id)

            # Get business ideas
            business_ideas = BusinessIdea.objects.filter(owner=user)

            # Get latest business analysis
            latest_analysis = BusinessAnalysis.objects.filter(
                user=user,
                status='completed'
            ).first()

            # Get forum activity
            forum_activity = self._get_forum_activity(user)

            # Get mentorship status
            mentorship_status = self._get_mentorship_status(user)

            # Determine business stage
            business_stage = self._determine_business_stage(user, business_ideas)

            # Identify pain points
            pain_points = self._identify_pain_points(user, business_ideas, latest_analysis)

            # Get recent activity
            recent_activity = self._get_recent_activity(user)

            context = {
                'user_id': user_id,
                'user_profile': {
                    'name': user.get_full_name(),
                    'email': user.email,
                    'date_joined': user.date_joined,
                    'profile_completion': self._calculate_profile_completion(user)
                },
                'business_ideas': [
                    {
                        'id': idea.id,
                        'title': idea.title,
                        'stage': idea.current_stage,
                        'created_at': idea.created_at,
                        'last_updated': idea.updated_at
                    } for idea in business_ideas
                ],
                'business_stage': business_stage,
                'latest_analysis': {
                    'viability_score': latest_analysis.viability_score if latest_analysis else None,
                    'funding_readiness': latest_analysis.funding_readiness_score if latest_analysis else None,
                    'last_analyzed': latest_analysis.completed_at if latest_analysis else None
                } if latest_analysis else None,
                'forum_activity': forum_activity,
                'mentorship_status': mentorship_status,
                'pain_points': pain_points,
                'recent_activity': recent_activity,
                'engagement_level': self._calculate_engagement_level(user),
                'next_milestones': self._identify_next_milestones(business_ideas),
                'context_timestamp': datetime.now().isoformat()
            }

            return context

        except Exception as e:
            logger.error(f"Error building user context for user {user_id}: {e}")
            return {'error': str(e), 'user_id': user_id}

    def _get_forum_activity(self, user: User) -> Dict[str, Any]:
        """Get user's forum activity summary"""
        threads_created = ForumThread.objects.filter(author=user).count()
        posts_created = ForumPost.objects.filter(author=user).count()
        recent_posts = ForumPost.objects.filter(
            author=user,
            created_at__gte=timezone.now() - timedelta(days=7)
        ).count()

        return {
            'threads_created': threads_created,
            'posts_created': posts_created,
            'recent_posts': recent_posts,
            'engagement_level': 'high' if recent_posts > 5 else 'medium' if recent_posts > 1 else 'low'
        }

    def _get_mentorship_status(self, user: User) -> Dict[str, Any]:
        """Get user's mentorship status"""
        # Check if user is a mentor
        is_mentor = hasattr(user, 'mentor_profile')

        # Check active mentorships (this would depend on your mentorship model)
        active_mentorships = 0  # Placeholder - implement based on your mentorship model

        return {
            'is_mentor': is_mentor,
            'active_mentorships': active_mentorships,
            'seeking_mentor': False  # Implement logic to determine if user is seeking mentor
        }

    def _determine_business_stage(self, user: User, business_ideas) -> str:
        """Determine user's overall business development stage"""
        if not business_ideas.exists():
            return 'ideation'

        stages = [idea.current_stage for idea in business_ideas]

        # Determine most advanced stage
        stage_priority = {
            'concept': 1,
            'validation': 2,
            'development': 3,
            'scaling': 4,
            'established': 5
        }

        max_stage = max(stages, key=lambda x: stage_priority.get(x, 0))
        return max_stage

    def _identify_pain_points(self, user: User, business_ideas, latest_analysis) -> List[str]:
        """Identify current user pain points"""
        pain_points = []

        # No business ideas
        if not business_ideas.exists():
            pain_points.append('no_business_ideas')

        # Incomplete profile
        if self._calculate_profile_completion(user) < 70:
            pain_points.append('incomplete_profile')

        # No recent analysis
        if not latest_analysis or (
            latest_analysis.completed_at and
            latest_analysis.completed_at < timezone.now() - timedelta(days=30)
        ):
            pain_points.append('outdated_analysis')

        # Low viability score
        if latest_analysis and latest_analysis.viability_score and latest_analysis.viability_score < 6:
            pain_points.append('low_viability')

        # Stuck in same stage
        stuck_ideas = business_ideas.filter(
            updated_at__lt=timezone.now() - timedelta(days=14)
        )
        if stuck_ideas.exists():
            pain_points.append('stagnant_progress')

        return pain_points

    def _get_recent_activity(self, user: User) -> Dict[str, Any]:
        """Get user's recent platform activity"""
        last_week = timezone.now() - timedelta(days=7)

        return {
            'business_ideas_updated': BusinessIdea.objects.filter(
                owner=user,
                updated_at__gte=last_week
            ).count(),
            'forum_posts': ForumPost.objects.filter(
                author=user,
                created_at__gte=last_week
            ).count(),
            'analyses_run': BusinessAnalysis.objects.filter(
                user=user,
                created_at__gte=last_week
            ).count(),
            'last_login': user.last_login,
            'activity_level': self._calculate_activity_level(user, last_week)
        }

    def _calculate_profile_completion(self, user: User) -> int:
        """Calculate user profile completion percentage"""
        completion = 0

        # Basic info
        if user.first_name:
            completion += 20
        if user.last_name:
            completion += 20
        if user.email:
            completion += 20

        # Profile details (if profile model exists)
        if hasattr(user, 'profile'):
            profile = user.profile
            if hasattr(profile, 'bio') and profile.bio:
                completion += 20
            if hasattr(profile, 'skills') and profile.skills:
                completion += 20

        return completion

    def _calculate_engagement_level(self, user: User) -> str:
        """Calculate user engagement level"""
        last_week = timezone.now() - timedelta(days=7)

        activity_score = 0

        # Recent logins
        if user.last_login and user.last_login >= last_week:
            activity_score += 2

        # Business idea updates
        recent_updates = BusinessIdea.objects.filter(
            owner=user,
            updated_at__gte=last_week
        ).count()
        activity_score += min(recent_updates, 3)

        # Forum participation
        recent_posts = ForumPost.objects.filter(
            author=user,
            created_at__gte=last_week
        ).count()
        activity_score += min(recent_posts, 3)

        # Analysis activity
        recent_analyses = BusinessAnalysis.objects.filter(
            user=user,
            created_at__gte=last_week
        ).count()
        activity_score += min(recent_analyses * 2, 4)

        if activity_score >= 8:
            return 'high'
        elif activity_score >= 4:
            return 'medium'
        else:
            return 'low'

    def _calculate_activity_level(self, user: User, since_date) -> str:
        """Calculate activity level since given date"""
        total_activity = (
            BusinessIdea.objects.filter(owner=user, updated_at__gte=since_date).count() +
            ForumPost.objects.filter(author=user, created_at__gte=since_date).count() +
            BusinessAnalysis.objects.filter(user=user, created_at__gte=since_date).count()
        )

        if total_activity >= 10:
            return 'very_high'
        elif total_activity >= 5:
            return 'high'
        elif total_activity >= 2:
            return 'medium'
        else:
            return 'low'

    def _identify_next_milestones(self, business_ideas) -> List[Dict[str, Any]]:
        """Identify next milestones for user's business ideas"""
        milestones = []

        for idea in business_ideas:
            stage_milestones = {
                'concept': [
                    {'title': 'Complete Business Plan', 'priority': 'high'},
                    {'title': 'Market Research', 'priority': 'high'},
                    {'title': 'Define Target Audience', 'priority': 'medium'}
                ],
                'validation': [
                    {'title': 'Build MVP', 'priority': 'high'},
                    {'title': 'Customer Interviews', 'priority': 'high'},
                    {'title': 'Market Testing', 'priority': 'medium'}
                ],
                'development': [
                    {'title': 'Product Development', 'priority': 'high'},
                    {'title': 'Team Building', 'priority': 'medium'},
                    {'title': 'Funding Preparation', 'priority': 'medium'}
                ]
            }

            stage_specific = stage_milestones.get(idea.current_stage, [])
            for milestone in stage_specific:
                milestone['business_idea_id'] = idea.id
                milestone['business_idea_title'] = idea.title
                milestones.append(milestone)

        return milestones[:5]  # Return top 5 milestones

    def update_context_cache(self, user_id: int, updates: Dict[str, Any]):
        """Update specific parts of cached context"""
        cache_key = f"user_context_{user_id}"
        if cache_key in self.context_cache:
            self.context_cache[cache_key]['context'].update(updates)
            self.context_cache[cache_key]['timestamp'] = datetime.now()


# Global context engine instance
context_engine = ContextEngine()


class AIOrchestrator:
    """
    Central AI coordinator that manages all AI services and provides intelligent responses
    """

    def __init__(self):
        self.context_engine = context_engine
        # Import here to avoid circular imports
        from .autonomous_advisor import autonomous_advisor
        self.business_advisor = autonomous_advisor

        # Initialize other AI modules (will be implemented)
        self.market_engine = None  # MarketIntelligenceEngine()
        self.predictor = None      # SuccessPredictor()
        self.task_manager = None   # AITaskManager()

    async def process_user_session(self, user_id: int) -> Dict[str, Any]:
        """Main AI processing loop for user session"""
        try:
            # Get comprehensive user context
            context = await self.context_engine.get_user_context(user_id)

            # Generate insights from business advisor
            advisor_insights = await self.business_advisor.analyze_user_journey(context)

            # Generate insights from other AI modules (when implemented)
            market_insights = await self._get_market_insights(context) if self.market_engine else {}
            predictions = await self._get_predictions(context) if self.predictor else {}
            task_suggestions = await self._get_task_suggestions(context) if self.task_manager else {}

            # Combine and prioritize all insights
            combined_insights = self._combine_insights(
                advisor_insights,
                market_insights,
                predictions,
                task_suggestions
            )

            # Generate intelligent dashboard data
            dashboard_data = self._generate_dashboard_data(context, combined_insights)

            # Update context cache with new insights
            self.context_engine.update_context_cache(user_id, {
                'last_ai_analysis': datetime.now().isoformat(),
                'latest_insights': combined_insights
            })

            return {
                'user_id': user_id,
                'context': context,
                'insights': combined_insights,
                'dashboard': dashboard_data,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error processing user session for user {user_id}: {e}")
            return {
                'error': str(e),
                'user_id': user_id,
                'timestamp': datetime.now().isoformat()
            }

    async def get_proactive_recommendations(self, user_id: int) -> List[Dict[str, Any]]:
        """Get proactive recommendations for user"""
        context = await self.context_engine.get_user_context(user_id)
        advisor_insights = await self.business_advisor.analyze_user_journey(context)

        return advisor_insights.get('recommendations', [])

    async def get_intelligent_alerts(self, user_id: int) -> List[Dict[str, Any]]:
        """Get intelligent alerts for user"""
        context = await self.context_engine.get_user_context(user_id)
        advisor_insights = await self.business_advisor.analyze_user_journey(context)

        return advisor_insights.get('alerts', [])

    async def get_next_actions(self, user_id: int) -> List[Dict[str, Any]]:
        """Get next recommended actions for user"""
        context = await self.context_engine.get_user_context(user_id)
        advisor_insights = await self.business_advisor.analyze_user_journey(context)

        return advisor_insights.get('next_actions', [])

    async def get_progress_analysis(self, user_id: int) -> Dict[str, Any]:
        """Get user progress analysis"""
        context = await self.context_engine.get_user_context(user_id)
        advisor_insights = await self.business_advisor.analyze_user_journey(context)

        return {
            'progress_score': advisor_insights.get('progress_score', {}),
            'situation_analysis': advisor_insights.get('situation_analysis', {}),
            'bottlenecks': advisor_insights.get('bottlenecks', []),
            'engagement_insights': advisor_insights.get('engagement_insights', {})
        }

    def _combine_insights(self, *insight_sources) -> Dict[str, Any]:
        """Combine insights from multiple AI sources"""
        combined = {
            'recommendations': [],
            'alerts': [],
            'next_actions': [],
            'predictions': [],
            'opportunities': []
        }

        for source in insight_sources:
            if isinstance(source, dict):
                for key in combined.keys():
                    if key in source:
                        if isinstance(source[key], list):
                            combined[key].extend(source[key])
                        else:
                            combined[key].append(source[key])

        # Prioritize and limit results
        for key in combined.keys():
            if isinstance(combined[key], list):
                combined[key] = self._prioritize_items(combined[key])[:10]

        return combined

    def _prioritize_items(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prioritize items by importance"""
        priority_order = {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}

        return sorted(
            items,
            key=lambda x: priority_order.get(x.get('priority', 'low'), 1),
            reverse=True
        )

    def _generate_dashboard_data(self, context: Dict[str, Any], insights: Dict[str, Any]) -> Dict[str, Any]:
        """Generate data for intelligent dashboard"""
        return {
            'business_health_score': self._calculate_business_health_score(context, insights),
            'priority_actions': insights.get('next_actions', [])[:5],
            'critical_alerts': [
                alert for alert in insights.get('alerts', [])
                if alert.get('urgency') in ['critical', 'high']
            ][:3],
            'opportunities': insights.get('opportunities', [])[:3],
            'progress_summary': self._generate_progress_summary(context, insights),
            'engagement_status': context.get('engagement_level', 'low'),
            'last_updated': datetime.now().isoformat()
        }

    def _calculate_business_health_score(self, context: Dict[str, Any], insights: Dict[str, Any]) -> float:
        """Calculate overall business health score"""
        # Base score from progress analysis
        progress_score = 0
        if 'progress_score' in insights:
            progress_score = insights['progress_score'].get('overall_score', 0)

        # Adjust based on alerts
        alert_penalty = 0
        for alert in insights.get('alerts', []):
            if alert.get('urgency') == 'critical':
                alert_penalty += 10
            elif alert.get('urgency') == 'high':
                alert_penalty += 5

        # Adjust based on engagement
        engagement_bonus = {
            'high': 5,
            'medium': 0,
            'low': -5
        }.get(context.get('engagement_level', 'low'), 0)

        final_score = max(0, min(100, progress_score - alert_penalty + engagement_bonus))
        return round(final_score, 1)

    def _generate_progress_summary(self, context: Dict[str, Any], insights: Dict[str, Any]) -> Dict[str, Any]:
        """Generate progress summary"""
        return {
            'current_stage': context.get('business_stage', 'ideation'),
            'ideas_count': len(context.get('business_ideas', [])),
            'recent_activity': context.get('recent_activity', {}),
            'next_milestone': insights.get('next_actions', [{}])[0].get('title', 'No actions defined') if insights.get('next_actions') else 'No actions defined',
            'completion_percentage': insights.get('progress_score', {}).get('overall_score', 0)
        }

    # Real AI-powered methods using Gemini service
    async def _get_market_insights(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get market intelligence insights using AI"""
        try:
            if not ai_is_available():
                logger.warning("AI service not available for market insights")
                return {'opportunities': [], 'threats': [], 'trends': [], 'error': 'AI service unavailable'}

            # Prepare context for AI analysis
            business_context = context.get('business_idea', {})
            industry = business_context.get('industry', 'General')
            title = business_context.get('title', 'Business Idea')

            prompt_context = {
                'business_title': title,
                'industry': industry,
                'analysis_type': 'market_insights'
            }

            response = ai_generate_intelligent_content('market_analysis', prompt_context, 'en')

            if response.get('success'):
                content = response.get('data', {}).get('content', '')
                # Parse AI response into structured insights
                return {
                    'opportunities': self._extract_opportunities(content),
                    'threats': self._extract_threats(content),
                    'trends': self._extract_trends(content),
                    'raw_analysis': content
                }
            else:
                logger.error(f"AI market insights failed: {response.get('error')}")
                return {'opportunities': [], 'threats': [], 'trends': [], 'error': response.get('error')}

        except Exception as e:
            logger.error(f"Error getting market insights: {e}")
            return {'opportunities': [], 'threats': [], 'trends': [], 'error': str(e)}

    async def _get_predictions(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get predictive insights using AI"""
        try:
            if not ai_is_available():
                logger.warning("AI service not available for predictions")
                return {'predictions': [], 'forecasts': [], 'error': 'AI service unavailable'}

            business_context = context.get('business_idea', {})
            title = business_context.get('title', 'Business Idea')

            prompt_context = {
                'business_title': title,
                'analysis_type': 'predictive_analysis'
            }

            response = ai_generate_intelligent_content('business_predictions', prompt_context, 'en')

            if response.get('success'):
                content = response.get('data', {}).get('content', '')
                return {
                    'predictions': self._extract_predictions(content),
                    'forecasts': self._extract_forecasts(content),
                    'raw_analysis': content
                }
            else:
                logger.error(f"AI predictions failed: {response.get('error')}")
                return {'predictions': [], 'forecasts': [], 'error': response.get('error')}

        except Exception as e:
            logger.error(f"Error getting predictions: {e}")
            return {'predictions': [], 'forecasts': [], 'error': str(e)}

    async def _get_task_suggestions(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get task management suggestions using AI"""
        try:
            if not ai_is_available():
                logger.warning("AI service not available for task suggestions")
                return {'suggested_tasks': [], 'deadlines': [], 'error': 'AI service unavailable'}

            business_context = context.get('business_idea', {})
            title = business_context.get('title', 'Business Idea')
            stage = business_context.get('current_stage', 'idea')

            prompt_context = {
                'business_title': title,
                'current_stage': stage,
                'analysis_type': 'task_suggestions'
            }

            response = ai_generate_intelligent_content('task_management', prompt_context, 'en')

            if response.get('success'):
                content = response.get('data', {}).get('content', '')
                return {
                    'suggested_tasks': self._extract_tasks(content),
                    'deadlines': self._extract_deadlines(content),
                    'raw_analysis': content
                }
            else:
                logger.error(f"AI task suggestions failed: {response.get('error')}")
                return {'suggested_tasks': [], 'deadlines': [], 'error': response.get('error')}

        except Exception as e:
            logger.error(f"Error getting task suggestions: {e}")
            return {'suggested_tasks': [], 'deadlines': [], 'error': str(e)}

    # Helper methods to parse AI responses
    def _extract_opportunities(self, content: str) -> List[str]:
        """Extract opportunities from AI analysis"""
        opportunities = []
        lines = content.split('\n')
        for line in lines:
            if 'opportunity' in line.lower() or 'potential' in line.lower():
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    opportunities.append(clean_line)
        return opportunities[:5]  # Limit to top 5

    def _extract_threats(self, content: str) -> List[str]:
        """Extract threats from AI analysis"""
        threats = []
        lines = content.split('\n')
        for line in lines:
            if 'threat' in line.lower() or 'risk' in line.lower() or 'challenge' in line.lower():
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    threats.append(clean_line)
        return threats[:5]  # Limit to top 5

    def _extract_trends(self, content: str) -> List[str]:
        """Extract trends from AI analysis"""
        trends = []
        lines = content.split('\n')
        for line in lines:
            if 'trend' in line.lower() or 'growing' in line.lower() or 'emerging' in line.lower():
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    trends.append(clean_line)
        return trends[:5]  # Limit to top 5

    def _extract_predictions(self, content: str) -> List[Dict[str, Any]]:
        """Extract predictions from AI analysis"""
        predictions = []
        lines = content.split('\n')
        for line in lines:
            if 'predict' in line.lower() or 'expect' in line.lower() or 'forecast' in line.lower():
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    predictions.append({
                        'description': clean_line,
                        'confidence': 0.7,  # Default confidence
                        'timeframe': '6-12 months'
                    })
        return predictions[:3]  # Limit to top 3

    def _extract_forecasts(self, content: str) -> List[Dict[str, Any]]:
        """Extract forecasts from AI analysis"""
        forecasts = []
        lines = content.split('\n')
        for line in lines:
            if 'forecast' in line.lower() or 'projection' in line.lower():
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    forecasts.append({
                        'metric': 'General',
                        'prediction': clean_line,
                        'timeframe': '12 months'
                    })
        return forecasts[:3]  # Limit to top 3

    def _extract_tasks(self, content: str) -> List[Dict[str, Any]]:
        """Extract tasks from AI analysis"""
        tasks = []
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['task', 'action', 'step', 'todo', 'should']):
                clean_line = line.strip('• - * 1234567890. ').strip()
                if clean_line and len(clean_line) > 10:
                    tasks.append({
                        'title': clean_line[:50] + '...' if len(clean_line) > 50 else clean_line,
                        'description': clean_line,
                        'priority': 'medium',
                        'estimated_hours': 2
                    })
        return tasks[:5]  # Limit to top 5

    def _extract_deadlines(self, content: str) -> List[Dict[str, Any]]:
        """Extract deadlines from AI analysis"""
        deadlines = []
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['deadline', 'due', 'complete by', 'finish']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    deadlines.append({
                        'task': clean_line,
                        'deadline': (datetime.now() + timedelta(days=30)).isoformat(),
                        'priority': 'medium'
                    })
        return deadlines[:3]  # Limit to top 3


# Global orchestrator instance
ai_orchestrator = AIOrchestrator()
