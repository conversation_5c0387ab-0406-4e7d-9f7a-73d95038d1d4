"""
Enhanced Predictive Analytics Helper Methods
Supporting functions for advanced predictive analytics features
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

# Import AI service for real predictions
from core.ai_service import ai_generate_intelligent_content, ai_is_available

logger = logging.getLogger(__name__)


class PredictiveAnalyticsHelpers:
    """Helper methods for enhanced predictive analytics"""
    
    @staticmethod
    def extract_failure_features(business_data: Dict[str, Any], historical_data: List[Dict] = None) -> np.ndarray:
        """Extract comprehensive features for failure prediction"""
        features = []
        
        # Basic business features
        features.extend([
            len(business_data.get('title', '')),
            len(business_data.get('description', '')),
            business_data.get('stage', 1),
            1 if business_data.get('has_team', False) else 0,
            1 if business_data.get('has_funding', False) else 0,
        ])
        
        # Financial health indicators
        features.extend([
            business_data.get('monthly_revenue', 0),
            business_data.get('burn_rate', 0),
            business_data.get('runway_months', 0),
            business_data.get('customer_count', 0),
            business_data.get('growth_rate', 0),
        ])
        
        # Team and operational features
        features.extend([
            business_data.get('team_size', 1),
            business_data.get('founder_experience', 0),
            business_data.get('market_size', 0),
            business_data.get('competition_level', 5),  # 1-10 scale
        ])
        
        # Historical trend features
        if historical_data:
            recent_data = historical_data[-3:] if len(historical_data) >= 3 else historical_data
            features.extend([
                len(recent_data),
                np.mean([d.get('progress_rate', 0) for d in recent_data]),
                np.std([d.get('progress_rate', 0) for d in recent_data]) if len(recent_data) > 1 else 0,
            ])
        else:
            features.extend([0, 0, 0])
        
        return np.array(features).reshape(1, -1)
    
    @staticmethod
    def extract_timing_features(business_data: Dict[str, Any], market_context: Dict[str, Any] = None) -> np.ndarray:
        """Extract features for market timing optimization"""
        features = []
        
        # Business readiness features
        features.extend([
            business_data.get('product_readiness', 0.5),  # 0-1 scale
            business_data.get('team_readiness', 0.5),
            business_data.get('funding_readiness', 0.5),
            business_data.get('market_validation', 0.5),
        ])
        
        # Market condition features
        if market_context:
            features.extend([
                market_context.get('market_growth_rate', 0.03),
                market_context.get('competition_intensity', 0.5),
                market_context.get('economic_conditions', 0.5),
                market_context.get('seasonal_factor', 0.5),
            ])
        else:
            features.extend([0.03, 0.5, 0.5, 0.5])
        
        # Competitive landscape features
        features.extend([
            business_data.get('competitive_advantage_score', 0.5),
            business_data.get('differentiation_level', 0.5),
            business_data.get('barrier_to_entry', 0.5),
        ])
        
        return np.array(features).reshape(1, -1)
    
    @staticmethod
    def extract_competitor_features(business_data: Dict[str, Any], competitor_data: List[Dict] = None) -> np.ndarray:
        """Extract features for competitor analysis"""
        features = []
        
        # Business positioning features
        features.extend([
            business_data.get('market_share', 0.01),
            business_data.get('brand_strength', 0.5),
            business_data.get('product_quality', 0.5),
            business_data.get('pricing_position', 0.5),  # relative to market
        ])
        
        # Competitive landscape features
        if competitor_data:
            competitor_count = len(competitor_data)
            avg_competitor_strength = np.mean([c.get('strength_score', 0.5) for c in competitor_data])
            max_competitor_threat = max([c.get('threat_level', 0.5) for c in competitor_data])
        else:
            competitor_count = 5  # default assumption
            avg_competitor_strength = 0.5
            max_competitor_threat = 0.5
        
        features.extend([
            competitor_count,
            avg_competitor_strength,
            max_competitor_threat,
        ])
        
        # Market dynamics
        features.extend([
            business_data.get('market_growth_rate', 0.03),
            business_data.get('customer_switching_cost', 0.5),
            business_data.get('network_effects', 0.3),
        ])
        
        return np.array(features).reshape(1, -1)
    
    @staticmethod
    def extract_cac_features(business_data: Dict[str, Any], marketing_data: Dict[str, Any] = None) -> np.ndarray:
        """Extract features for CAC prediction"""
        features = []
        
        # Business characteristics
        features.extend([
            business_data.get('industry_category', 1),  # encoded category
            business_data.get('business_model', 1),  # encoded model type
            business_data.get('target_market_size', 1000000),
            business_data.get('product_complexity', 0.5),
        ])
        
        # Marketing context
        if marketing_data:
            features.extend([
                marketing_data.get('total_marketing_budget', 10000),
                marketing_data.get('channel_count', 3),
                marketing_data.get('conversion_rate', 0.02),
                marketing_data.get('brand_awareness', 0.1),
            ])
        else:
            features.extend([10000, 3, 0.02, 0.1])
        
        # Market conditions
        features.extend([
            business_data.get('competition_level', 5),
            business_data.get('market_maturity', 0.5),
            business_data.get('customer_education_needed', 0.5),
        ])
        
        return np.array(features).reshape(1, -1)
    
    @staticmethod
    def generate_early_warning_indicators(business_data: Dict[str, Any], failure_probability: float) -> List[Dict[str, Any]]:
        """Generate early warning indicators for startup failure"""
        indicators = []
        
        # Financial warning indicators
        if business_data.get('burn_rate', 0) > business_data.get('monthly_revenue', 0) * 2:
            indicators.append({
                'type': 'financial',
                'severity': 'high',
                'indicator': 'High burn rate relative to revenue',
                'recommendation': 'Reduce expenses or increase revenue urgently'
            })
        
        # Growth warning indicators
        if business_data.get('growth_rate', 0) < 0.05:  # Less than 5% monthly growth
            indicators.append({
                'type': 'growth',
                'severity': 'medium',
                'indicator': 'Low growth rate',
                'recommendation': 'Focus on customer acquisition and retention'
            })
        
        # Market warning indicators
        if business_data.get('customer_count', 0) < 100:
            indicators.append({
                'type': 'market',
                'severity': 'medium',
                'indicator': 'Limited customer base',
                'recommendation': 'Accelerate customer acquisition efforts'
            })
        
        # Team warning indicators
        if business_data.get('team_size', 1) < 2:
            indicators.append({
                'type': 'team',
                'severity': 'low',
                'indicator': 'Single founder risk',
                'recommendation': 'Consider bringing on co-founders or key hires'
            })
        
        return indicators
    
    @staticmethod
    def calculate_detailed_risk_factors(business_data: Dict[str, Any], historical_data: List[Dict] = None) -> Dict[str, Any]:
        """Calculate detailed risk factors for failure prediction"""
        risk_factors = {}
        
        # Financial risk factors
        runway_months = business_data.get('runway_months', 12)
        if runway_months < 6:
            risk_factors['financial_runway'] = {
                'score': 0.9,
                'description': 'Critical: Less than 6 months runway',
                'impact': 'high'
            }
        elif runway_months < 12:
            risk_factors['financial_runway'] = {
                'score': 0.6,
                'description': 'Warning: Less than 12 months runway',
                'impact': 'medium'
            }
        
        # Market risk factors
        market_size = business_data.get('market_size', 1000000)
        if market_size < 100000:
            risk_factors['market_size'] = {
                'score': 0.8,
                'description': 'Small addressable market',
                'impact': 'high'
            }
        
        # Competition risk factors
        competition_level = business_data.get('competition_level', 5)
        if competition_level > 8:
            risk_factors['high_competition'] = {
                'score': 0.7,
                'description': 'Highly competitive market',
                'impact': 'medium'
            }
        
        return risk_factors
    
    @staticmethod
    def determine_alert_level(failure_probability: float) -> str:
        """Determine alert level based on failure probability"""
        if failure_probability > 0.7:
            return 'critical'
        elif failure_probability > 0.5:
            return 'high'
        elif failure_probability > 0.3:
            return 'medium'
        else:
            return 'low'
    
    @staticmethod
    def generate_monitoring_schedule(alert_level: str) -> Dict[str, Any]:
        """Generate monitoring schedule based on alert level"""
        schedules = {
            'critical': {
                'frequency': 'weekly',
                'metrics': ['cash_flow', 'customer_churn', 'team_morale'],
                'review_interval_days': 7
            },
            'high': {
                'frequency': 'bi-weekly',
                'metrics': ['revenue_growth', 'customer_acquisition', 'burn_rate'],
                'review_interval_days': 14
            },
            'medium': {
                'frequency': 'monthly',
                'metrics': ['growth_metrics', 'market_position', 'team_performance'],
                'review_interval_days': 30
            },
            'low': {
                'frequency': 'quarterly',
                'metrics': ['strategic_goals', 'market_trends', 'competitive_position'],
                'review_interval_days': 90
            }
        }
        
        return schedules.get(alert_level, schedules['medium'])
    
    @staticmethod
    def calculate_next_review_date(alert_level: str) -> str:
        """Calculate next review date based on alert level"""
        schedule = PredictiveAnalyticsHelpers.generate_monitoring_schedule(alert_level)
        next_date = datetime.now() + timedelta(days=schedule['review_interval_days'])
        return next_date.isoformat()

    @staticmethod
    def analyze_current_market_conditions(business_data: Dict[str, Any], market_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze current market conditions for timing optimization"""
        if not market_context:
            market_context = {}

        return {
            'market_growth_rate': market_context.get('market_growth_rate', 0.03),
            'economic_conditions': market_context.get('economic_conditions', 0.7),
            'industry_trends': market_context.get('industry_trends', ['digital_transformation', 'sustainability']),
            'regulatory_environment': market_context.get('regulatory_environment', 'stable'),
            'funding_availability': market_context.get('funding_availability', 0.6),
            'talent_availability': market_context.get('talent_availability', 0.5),
            'market_sentiment': market_context.get('market_sentiment', 'optimistic')
        }

    @staticmethod
    def determine_recommended_action(timing_scores: Dict[str, float]) -> str:
        """Determine recommended action based on timing scores"""
        max_score = max(timing_scores.values())
        recommended_action = max(timing_scores, key=timing_scores.get)

        if max_score > 0.8:
            return f"Strongly recommend: {recommended_action.replace('_', ' ').title()}"
        elif max_score > 0.6:
            return f"Consider: {recommended_action.replace('_', ' ').title()}"
        else:
            return "Wait and reassess market conditions"

    @staticmethod
    def analyze_individual_competitors(competitor_data: List[Dict]) -> List[Dict[str, Any]]:
        """Analyze individual competitors"""
        if not competitor_data:
            return [{
                'name': 'Generic Competitor',
                'threat_level': 0.5,
                'strengths': ['Market presence'],
                'weaknesses': ['Limited innovation'],
                'market_share': 0.1,
                'recent_activities': ['Product updates']
            }]

        analyzed_competitors = []
        for competitor in competitor_data:
            analysis = {
                'name': competitor.get('name', 'Unknown'),
                'threat_level': competitor.get('threat_level', 0.5),
                'strengths': competitor.get('strengths', ['Market presence']),
                'weaknesses': competitor.get('weaknesses', ['Unknown']),
                'market_share': competitor.get('market_share', 0.05),
                'recent_activities': competitor.get('recent_activities', []),
                'funding_status': competitor.get('funding_status', 'Unknown'),
                'growth_rate': competitor.get('growth_rate', 0.0)
            }
            analyzed_competitors.append(analysis)

        return analyzed_competitors

    @staticmethod
    def analyze_cac_by_channel(marketing_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze CAC by marketing channel"""
        channels = marketing_data.get('channels', {})

        if not channels:
            # Default channel analysis
            channels = {
                'organic_search': {'spend': 0, 'acquisitions': 10, 'cac': 0},
                'paid_search': {'spend': 5000, 'acquisitions': 50, 'cac': 100},
                'social_media': {'spend': 3000, 'acquisitions': 30, 'cac': 100},
                'content_marketing': {'spend': 2000, 'acquisitions': 25, 'cac': 80},
                'referrals': {'spend': 500, 'acquisitions': 15, 'cac': 33}
            }

        channel_analysis = {}
        for channel, data in channels.items():
            spend = data.get('spend', 0)
            acquisitions = data.get('acquisitions', 1)
            cac = spend / acquisitions if acquisitions > 0 else spend

            channel_analysis[channel] = {
                'cac': round(cac, 2),
                'efficiency_score': PredictiveAnalyticsHelpers._calculate_channel_efficiency(cac),
                'volume': acquisitions,
                'spend': spend,
                'recommendation': PredictiveAnalyticsHelpers._get_channel_recommendation(cac, acquisitions)
            }

        return channel_analysis

    @staticmethod
    def _calculate_channel_efficiency(cac: float) -> float:
        """Calculate channel efficiency score"""
        if cac < 50:
            return 0.9
        elif cac < 100:
            return 0.7
        elif cac < 200:
            return 0.5
        else:
            return 0.3

    @staticmethod
    def _get_channel_recommendation(cac: float, volume: int) -> str:
        """Get recommendation for marketing channel"""
        if cac < 50 and volume > 10:
            return "Scale up - high efficiency"
        elif cac < 100:
            return "Optimize and maintain"
        elif cac < 200:
            return "Optimize or reduce spend"
        else:
            return "Consider pausing or major optimization"

    @staticmethod
    def calculate_ltv_cac_ratio(predicted_cac: float, business_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate LTV:CAC ratio analysis"""
        # Estimate LTV based on business data
        monthly_revenue_per_customer = business_data.get('monthly_revenue_per_customer', 50)
        churn_rate = business_data.get('monthly_churn_rate', 0.05)
        gross_margin = business_data.get('gross_margin', 0.7)

        # Simple LTV calculation: (Monthly Revenue * Gross Margin) / Churn Rate
        ltv = (monthly_revenue_per_customer * gross_margin) / churn_rate if churn_rate > 0 else monthly_revenue_per_customer * 20

        ltv_cac_ratio = ltv / predicted_cac if predicted_cac > 0 else 0

        return {
            'ltv': round(ltv, 2),
            'cac': round(predicted_cac, 2),
            'ltv_cac_ratio': round(ltv_cac_ratio, 2),
            'ratio_category': PredictiveAnalyticsHelpers._categorize_ltv_cac_ratio(ltv_cac_ratio),
            'payback_period_months': round(predicted_cac / (monthly_revenue_per_customer * gross_margin), 1) if monthly_revenue_per_customer > 0 else 0,
            'recommendation': PredictiveAnalyticsHelpers._get_ltv_cac_recommendation(ltv_cac_ratio)
        }

    @staticmethod
    def _categorize_ltv_cac_ratio(ratio: float) -> str:
        """Categorize LTV:CAC ratio"""
        if ratio >= 3:
            return "Excellent"
        elif ratio >= 2:
            return "Good"
        elif ratio >= 1:
            return "Acceptable"
        else:
            return "Poor"

    @staticmethod
    def _get_ltv_cac_recommendation(ratio: float) -> str:
        """Get recommendation based on LTV:CAC ratio"""
        if ratio >= 3:
            return "Strong unit economics - consider scaling marketing"
        elif ratio >= 2:
            return "Healthy ratio - optimize for growth"
        elif ratio >= 1:
            return "Break-even - focus on improving LTV or reducing CAC"
        else:
            return "Unsustainable - urgent optimization needed"

    # AI-powered prediction methods using real Gemini service
    @staticmethod
    def ai_failure_prediction(business_data: Dict[str, Any]) -> Dict[str, Any]:
        """AI-powered failure prediction using Gemini"""
        try:
            if not ai_is_available():
                logger.error("AI service not available for failure prediction")
                return {'error': 'AI service not available', 'timestamp': datetime.now().isoformat()}

            # Prepare context for AI analysis
            context = {
                'business_title': business_data.get('title', 'Business Idea'),
                'industry': business_data.get('industry', 'General'),
                'stage': business_data.get('stage', 'idea'),
                'analysis_type': 'failure_prediction'
            }

            response = ai_generate_intelligent_content('risk_analysis', context, 'en')

            if response.get('success'):
                content = response.get('data', {}).get('content', '')

                # Parse AI response and extract structured data
                return {
                    'failure_probability': PredictiveAnalyticsHelpers._extract_probability(content, 'failure'),
                    'success_probability': PredictiveAnalyticsHelpers._extract_probability(content, 'success'),
                    'confidence_score': 0.8,  # High confidence for AI analysis
                    'alert_level': PredictiveAnalyticsHelpers._extract_alert_level(content),
                    'warning_indicators': PredictiveAnalyticsHelpers._extract_warnings(content),
                    'risk_factors': PredictiveAnalyticsHelpers._extract_risk_factors(content),
                    'early_warning_signals': PredictiveAnalyticsHelpers._extract_signals(content),
                    'prevention_recommendations': PredictiveAnalyticsHelpers._extract_recommendations(content),
                    'monitoring_schedule': PredictiveAnalyticsHelpers.generate_monitoring_schedule('high'),
                    'next_review_date': PredictiveAnalyticsHelpers.calculate_next_review_date('high'),
                    'ai_analysis': content,
                    'timestamp': datetime.now().isoformat(),
                    'source': 'AI-powered analysis'
                }
            else:
                logger.error(f"AI failure prediction failed: {response.get('error')}")
                return {'error': response.get('error'), 'timestamp': datetime.now().isoformat()}

        except Exception as e:
            logger.error(f"Error in AI failure prediction: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}

    @staticmethod
    def ai_timing_optimization(business_data: Dict[str, Any]) -> Dict[str, Any]:
        """AI-powered timing optimization using Gemini"""
        try:
            if not ai_is_available():
                logger.error("AI service not available for timing optimization")
                return {'error': 'AI service not available', 'timestamp': datetime.now().isoformat()}

            context = {
                'business_title': business_data.get('title', 'Business Idea'),
                'industry': business_data.get('industry', 'General'),
                'stage': business_data.get('stage', 'idea'),
                'analysis_type': 'timing_optimization'
            }

            response = ai_generate_intelligent_content('timing_analysis', context, 'en')

            if response.get('success'):
                content = response.get('data', {}).get('content', '')

                return {
                    'timing_scores': PredictiveAnalyticsHelpers._extract_timing_scores(content),
                    'recommended_action': PredictiveAnalyticsHelpers._extract_recommended_action(content),
                    'optimal_timing_windows': PredictiveAnalyticsHelpers._extract_timing_windows(content),
                    'market_conditions': PredictiveAnalyticsHelpers.analyze_current_market_conditions(business_data),
                    'timing_recommendations': PredictiveAnalyticsHelpers._extract_recommendations(content),
                    'risk_factors': PredictiveAnalyticsHelpers._extract_risk_factors_list(content),
                    'competitive_timing': PredictiveAnalyticsHelpers._extract_competitive_timing(content),
                    'seasonal_factors': PredictiveAnalyticsHelpers._extract_seasonal_factors(content),
                    'readiness_assessment': PredictiveAnalyticsHelpers._extract_readiness(content),
                    'confidence_level': 0.8,
                    'ai_analysis': content,
                    'timestamp': datetime.now().isoformat(),
                    'source': 'AI-powered analysis'
                }
            else:
                logger.error(f"AI timing optimization failed: {response.get('error')}")
                return {'error': response.get('error'), 'timestamp': datetime.now().isoformat()}

        except Exception as e:
            logger.error(f"Error in AI timing optimization: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}

    @staticmethod
    def ai_competitor_analysis(business_data: Dict[str, Any]) -> Dict[str, Any]:
        """AI-powered competitor analysis using Gemini"""
        try:
            if not ai_is_available():
                logger.error("AI service not available for competitor analysis")
                return {'error': 'AI service not available', 'timestamp': datetime.now().isoformat()}

            context = {
                'business_title': business_data.get('title', 'Business Idea'),
                'industry': business_data.get('industry', 'General'),
                'description': business_data.get('description', ''),
                'analysis_type': 'competitor_analysis'
            }

            response = ai_generate_intelligent_content('competitive_analysis', context, 'en')

            if response.get('success'):
                content = response.get('data', {}).get('content', '')

                return {
                    'competitive_threat_score': PredictiveAnalyticsHelpers._extract_threat_score(content),
                    'threat_level': PredictiveAnalyticsHelpers._extract_threat_level(content),
                    'competitor_analysis': PredictiveAnalyticsHelpers._extract_competitor_details(content),
                    'market_positioning': PredictiveAnalyticsHelpers._extract_market_positioning(content),
                    'competitive_gaps': PredictiveAnalyticsHelpers._extract_gaps(content),
                    'competitive_advantages': PredictiveAnalyticsHelpers._extract_advantages(content),
                    'monitoring_alerts': PredictiveAnalyticsHelpers._extract_monitoring_alerts(content),
                    'strategic_recommendations': PredictiveAnalyticsHelpers._extract_recommendations(content),
                    'market_share_analysis': PredictiveAnalyticsHelpers._extract_market_share_analysis(content),
                    'differentiation_opportunities': PredictiveAnalyticsHelpers._extract_differentiation(content),
                    'competitive_timeline': PredictiveAnalyticsHelpers._extract_timeline(content),
                    'next_analysis_date': (datetime.now() + timedelta(days=30)).isoformat(),
                    'ai_analysis': content,
                    'timestamp': datetime.now().isoformat(),
                    'source': 'AI-powered analysis'
                }
            else:
                logger.error(f"AI competitor analysis failed: {response.get('error')}")
                return {'error': response.get('error'), 'timestamp': datetime.now().isoformat()}

        except Exception as e:
            logger.error(f"Error in AI competitor analysis: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}

    @staticmethod
    def ai_cac_prediction(business_data: Dict[str, Any]) -> Dict[str, Any]:
        """AI-powered CAC prediction using Gemini"""
        try:
            if not ai_is_available():
                logger.error("AI service not available for CAC prediction")
                return {'error': 'AI service not available', 'timestamp': datetime.now().isoformat()}

            context = {
                'business_title': business_data.get('title', 'Business Idea'),
                'industry': business_data.get('industry', 'General'),
                'target_audience': business_data.get('target_audience', ''),
                'analysis_type': 'cac_prediction'
            }

            response = ai_generate_intelligent_content('cac_analysis', context, 'en')

            if response.get('success'):
                content = response.get('data', {}).get('content', '')
                predicted_cac = PredictiveAnalyticsHelpers._extract_cac_value(content)

                return {
                    'predicted_cac': predicted_cac,
                    'confidence_score': 0.8,
                    'cac_category': PredictiveAnalyticsHelpers._categorize_cac(predicted_cac),
                    'channel_analysis': PredictiveAnalyticsHelpers._extract_channel_analysis(content),
                    'cac_trends': PredictiveAnalyticsHelpers._extract_cac_trends(content),
                    'optimization_recommendations': PredictiveAnalyticsHelpers._extract_recommendations(content),
                    'industry_benchmarks': PredictiveAnalyticsHelpers._extract_benchmarks(content),
                    'ltv_cac_analysis': PredictiveAnalyticsHelpers._extract_ltv_analysis(content),
                    'warning_indicators': PredictiveAnalyticsHelpers._extract_warnings(content),
                    'cost_efficiency_score': PredictiveAnalyticsHelpers._calculate_efficiency_score(predicted_cac),
                    'recommended_budget_allocation': PredictiveAnalyticsHelpers._extract_budget_allocation(content),
                    'monitoring_metrics': PredictiveAnalyticsHelpers._extract_monitoring_metrics(content),
                    'next_review_date': (datetime.now() + timedelta(days=30)).isoformat(),
                    'ai_analysis': content,
                    'timestamp': datetime.now().isoformat(),
                    'source': 'AI-powered analysis'
                }
            else:
                logger.error(f"AI CAC prediction failed: {response.get('error')}")
                return {'error': response.get('error'), 'timestamp': datetime.now().isoformat()}

        except Exception as e:
            logger.error(f"Error in AI CAC prediction: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}

    # Helper methods for parsing AI responses
    @staticmethod
    def _extract_probability(content: str, prob_type: str) -> float:
        """Extract probability values from AI content"""
        # Simple extraction logic - can be enhanced
        if prob_type.lower() in content.lower():
            # Look for percentage patterns
            import re
            pattern = rf'{prob_type}.*?(\d+(?:\.\d+)?)[%]?'
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                value = float(match.group(1))
                return value / 100 if value > 1 else value
        return 0.5  # Default neutral probability

    @staticmethod
    def _extract_alert_level(content: str) -> str:
        """Extract alert level from AI content"""
        content_lower = content.lower()
        if any(word in content_lower for word in ['high risk', 'critical', 'urgent']):
            return 'high'
        elif any(word in content_lower for word in ['low risk', 'minimal', 'safe']):
            return 'low'
        return 'medium'

    @staticmethod
    def _extract_warnings(content: str) -> List[Dict[str, str]]:
        """Extract warning indicators from AI content"""
        warnings = []
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['warning', 'risk', 'concern', 'issue']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    warnings.append({
                        'type': 'ai_identified',
                        'severity': 'medium',
                        'indicator': clean_line,
                        'recommendation': 'Monitor closely'
                    })
        return warnings[:5]  # Limit to top 5

    @staticmethod
    def _extract_risk_factors(content: str) -> Dict[str, Dict[str, Any]]:
        """Extract risk factors from AI content"""
        risk_factors = {}
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'risk' in line.lower():
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    risk_factors[f'risk_{i}'] = {
                        'score': 0.5,
                        'description': clean_line,
                        'impact': 'medium'
                    }
        return risk_factors

    @staticmethod
    def _extract_signals(content: str) -> List[str]:
        """Extract early warning signals from AI content"""
        signals = []
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['signal', 'indicator', 'monitor', 'watch']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    signals.append(clean_line)
        return signals[:5]

    @staticmethod
    def _extract_recommendations(content: str) -> List[str]:
        """Extract recommendations from AI content"""
        recommendations = []
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['recommend', 'suggest', 'should', 'consider']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    recommendations.append(clean_line)
        return recommendations[:5]

    @staticmethod
    def _extract_cac_value(content: str) -> float:
        """Extract CAC value from AI content"""
        import re
        # Look for currency patterns
        pattern = r'\$(\d+(?:\.\d+)?)'
        match = re.search(pattern, content)
        if match:
            return float(match.group(1))

        # Look for number patterns with CAC context
        pattern = r'CAC.*?(\d+(?:\.\d+)?)'
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            return float(match.group(1))

        return 150.0  # Default value

    @staticmethod
    def _categorize_cac(cac_value: float) -> str:
        """Categorize CAC value"""
        if cac_value < 50:
            return 'low'
        elif cac_value < 200:
            return 'average'
        else:
            return 'high'

    @staticmethod
    def _calculate_efficiency_score(cac_value: float) -> float:
        """Calculate efficiency score based on CAC"""
        # Simple scoring logic
        if cac_value < 50:
            return 0.9
        elif cac_value < 150:
            return 0.7
        elif cac_value < 300:
            return 0.5
        else:
            return 0.3

    # Additional helper methods for parsing AI responses
    @staticmethod
    def _extract_timing_scores(content: str) -> Dict[str, float]:
        """Extract timing scores from AI content"""
        return {
            'launch_score': PredictiveAnalyticsHelpers._extract_score(content, 'launch'),
            'pivot_score': PredictiveAnalyticsHelpers._extract_score(content, 'pivot'),
            'scale_score': PredictiveAnalyticsHelpers._extract_score(content, 'scale')
        }

    @staticmethod
    def _extract_score(content: str, score_type: str) -> float:
        """Extract specific score from content"""
        import re
        pattern = rf'{score_type}.*?(\d+(?:\.\d+)?)'
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            value = float(match.group(1))
            return value / 100 if value > 1 else value
        return 0.6  # Default score

    @staticmethod
    def _extract_recommended_action(content: str) -> str:
        """Extract recommended action from AI content"""
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['recommend', 'action', 'should']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    return clean_line
        return 'Continue current strategy'

    @staticmethod
    def _extract_timing_windows(content: str) -> Dict[str, str]:
        """Extract timing windows from AI content"""
        return {
            'launch': PredictiveAnalyticsHelpers._extract_window(content, 'launch'),
            'pivot': PredictiveAnalyticsHelpers._extract_window(content, 'pivot'),
            'scale': PredictiveAnalyticsHelpers._extract_window(content, 'scale')
        }

    @staticmethod
    def _extract_window(content: str, window_type: str) -> str:
        """Extract specific timing window"""
        lines = content.split('\n')
        for line in lines:
            if window_type.lower() in line.lower():
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    return clean_line
        return f'Optimal {window_type} timing to be determined'

    @staticmethod
    def _extract_risk_factors_list(content: str) -> List[str]:
        """Extract risk factors as list from AI content"""
        risk_factors = []
        lines = content.split('\n')
        for line in lines:
            if 'risk' in line.lower():
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    risk_factors.append(clean_line)
        return risk_factors[:5]

    @staticmethod
    def _extract_competitive_timing(content: str) -> str:
        """Extract competitive timing information"""
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['competitor', 'competitive', 'competition']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    return clean_line
        return 'Monitor competitive landscape'

    @staticmethod
    def _extract_seasonal_factors(content: str) -> str:
        """Extract seasonal factors"""
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['seasonal', 'season', 'quarterly', 'monthly']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    return clean_line
        return 'Consider seasonal market patterns'

    @staticmethod
    def _extract_readiness(content: str) -> str:
        """Extract readiness assessment"""
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['ready', 'readiness', 'prepared']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    return clean_line
        return 'Readiness assessment pending'

    @staticmethod
    def _extract_threat_score(content: str) -> float:
        """Extract threat score from content"""
        return PredictiveAnalyticsHelpers._extract_score(content, 'threat')

    @staticmethod
    def _extract_threat_level(content: str) -> str:
        """Extract threat level from content"""
        content_lower = content.lower()
        if any(word in content_lower for word in ['high threat', 'severe', 'critical']):
            return 'high'
        elif any(word in content_lower for word in ['low threat', 'minimal', 'minor']):
            return 'low'
        return 'medium'

    @staticmethod
    def _extract_competitor_details(content: str) -> List[Dict[str, Any]]:
        """Extract competitor details from content"""
        competitors = []
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['competitor', 'rival', 'competition']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    competitors.append({
                        'name': 'Identified Competitor',
                        'description': clean_line,
                        'threat_level': 'medium'
                    })
        return competitors[:5]

    @staticmethod
    def _extract_market_positioning(content: str) -> Dict[str, Any]:
        """Extract market positioning from content"""
        return {
            'position': 'emerging',
            'differentiation': 'moderate',
            'market_share': 0.05
        }

    @staticmethod
    def _extract_gaps(content: str) -> List[str]:
        """Extract competitive gaps"""
        gaps = []
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['gap', 'opportunity', 'missing']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    gaps.append(clean_line)
        return gaps[:5]

    @staticmethod
    def _extract_advantages(content: str) -> List[str]:
        """Extract competitive advantages"""
        advantages = []
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['advantage', 'strength', 'benefit']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    advantages.append(clean_line)
        return advantages[:5]

    @staticmethod
    def _extract_monitoring_alerts(content: str) -> List[str]:
        """Extract monitoring alerts"""
        alerts = []
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['monitor', 'watch', 'track', 'alert']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    alerts.append(clean_line)
        return alerts[:5]

    @staticmethod
    def _extract_market_share_analysis(content: str) -> str:
        """Extract market share analysis"""
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['market share', 'share', 'market position']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    return clean_line
        return 'Market share analysis requires additional data'

    @staticmethod
    def _extract_differentiation(content: str) -> List[str]:
        """Extract differentiation opportunities"""
        opportunities = []
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['differentiat', 'unique', 'distinct']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    opportunities.append(clean_line)
        return opportunities[:5]

    @staticmethod
    def _extract_timeline(content: str) -> str:
        """Extract competitive timeline"""
        lines = content.split('\n')
        for line in lines:
            if any(word in line.lower() for word in ['timeline', 'schedule', 'timeframe']):
                clean_line = line.strip('• - * ').strip()
                if clean_line and len(clean_line) > 10:
                    return clean_line
        return 'Regular competitive monitoring recommended'

    @staticmethod
    def _extract_channel_analysis(content: str) -> Dict[str, Any]:
        """Extract channel analysis from content"""
        return {
            'organic_search': {'cac': 0, 'efficiency_score': 0.8, 'volume': 'medium'},
            'paid_search': {'cac': 100, 'efficiency_score': 0.6, 'volume': 'high'},
            'social_media': {'cac': 75, 'efficiency_score': 0.7, 'volume': 'medium'}
        }

    @staticmethod
    def _extract_cac_trends(content: str) -> Dict[str, Any]:
        """Extract CAC trends from content"""
        return {
            'trend': 'stable',
            'monthly_change': 0.0,
            'projection_3_months': 150
        }

    @staticmethod
    def _extract_benchmarks(content: str) -> Dict[str, float]:
        """Extract industry benchmarks"""
        return {
            'industry_average': 150,
            'top_quartile': 105,
            'bottom_quartile': 225
        }

    @staticmethod
    def _extract_ltv_analysis(content: str) -> Dict[str, Any]:
        """Extract LTV analysis"""
        return {
            'ltv_cac_ratio': 3.0,
            'assessment': 'healthy',
            'recommendation': 'maintain current strategy'
        }

    @staticmethod
    def _extract_budget_allocation(content: str) -> Dict[str, float]:
        """Extract budget allocation recommendations"""
        return {
            'organic_search': 0.3,
            'paid_search': 0.25,
            'social_media': 0.2,
            'content_marketing': 0.15,
            'referrals': 0.1
        }

    @staticmethod
    def _extract_monitoring_metrics(content: str) -> List[str]:
        """Extract monitoring metrics"""
        return ['CAC by channel', 'Conversion rates', 'LTV:CAC ratio', 'Customer lifetime value']
